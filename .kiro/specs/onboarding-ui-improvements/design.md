# Design Document

## Overview

This design improves the onboarding page UI by addressing spacing, typography, button styling, and visual hierarchy issues. The improvements will align the page with Vertoie's established design system while maintaining the existing functionality and user flow.

## Architecture

The design maintains the existing HTML structure and HTMX functionality while updating CSS classes and styling to match the design system. No backend changes are required - this is purely a frontend visual improvement.

### Current Structure Analysis
- Uses `.onboarding-card` with `.card-header`, `.card-body`, and `.card-footer` sections
- Implements proper form handling with HTMX
- Has responsive layout considerations

### Design System Integration
- Leverage existing CSS variables from `vertoie-design-system.css`
- Use established spacing scale (`--space-*` variables)
- Apply consistent typography hierarchy
- Implement proper button styling patterns

## Components and Interfaces

### Header Section Improvements
- **Typography**: Increase header title size to `2.8rem` (from current `2.4rem`) for better hierarchy
- **Spacing**: Add proper padding using `--space-8` for top/bottom, `--space-6` for sides
- **Description**: Improve line-height to `1.6` and use `--text-secondary` color
- **Visual Hierarchy**: Create clear separation between title and description

### Button Component Updates
- **Height**: Ensure consistent height with `.btn-lg` class (padding: `--space-4` `--space-6`)
- **Typography**: Use `font-weight: 600` and `font-size: 1.8rem`
- **Colors**: Apply proper Vertoie orange (`--vertoie-orange`) with hover states
- **Interaction**: Add proper focus states and hover animations
- **Spacing**: Ensure adequate margin from input field

### Examples Section Redesign
- **Background**: Use subtle background color (`--bg-tertiary`) for visual separation
- **Typography**: Improve readability with proper font sizes and line heights
- **List Styling**: Replace basic bullets with branded arrow indicators
- **Spacing**: Add consistent padding and gap between examples
- **Visual Polish**: Add subtle borders and rounded corners

### Input Field Enhancements
- **Sizing**: Increase padding to `--space-4` for better touch targets
- **Typography**: Use `font-size: 1.6rem` for better readability
- **Focus States**: Ensure proper focus ring with Vertoie orange
- **Placeholder**: Style with appropriate contrast and helpful text

## Data Models

No data model changes required - this is a pure UI improvement.

## Error Handling

No error handling changes required. Existing HTMX error handling remains intact.

## Testing Strategy

### Visual Testing
- Test across different screen sizes (mobile, tablet, desktop)
- Verify proper spacing and typography scaling
- Ensure button interactions work correctly
- Validate color contrast for accessibility

### Cross-browser Testing
- Test in Chrome, Firefox, Safari, and Edge
- Verify CSS variable support and fallbacks
- Test hover and focus states

### Responsive Testing
- Mobile-first approach validation
- Tablet layout verification
- Desktop layout optimization

## Implementation Approach

### CSS Updates
1. Update `.card-header` styling for improved spacing and typography
2. Enhance `.btn-primary` to match design system standards
3. Redesign `.card-footer` and `.examples-list` for better visual appeal
4. Improve `.form-input` styling for consistency

### Design System Alignment
- Use existing CSS variables for colors, spacing, and typography
- Leverage established component patterns from `vertoie-design-system.css`
- Maintain responsive design principles
- Ensure accessibility compliance

### Brand Consistency
- Apply Vertoie orange brand color consistently
- Use proper typography hierarchy
- Implement consistent spacing patterns
- Maintain visual cohesion with other pages