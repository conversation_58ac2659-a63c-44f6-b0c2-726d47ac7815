# Vertoie Project Overview

## Project Vision
**Vertoie** is an AI-generated business software builder that uses LLMs to create custom business applications. The platform generates complete Flutter applications tailored to specific business needs through conversational AI, with robust version management and schema evolution capabilities.

## Core Architecture Components

### 1. Vertoie Platform (Go/Fiber + HTMX)
- Web-based platform for business setup and management
- Conversational AI interface for business requirements gathering
- Module generation and customization tools
- User account and subscription management
- Version management and testing environments

### 2. Generated Business Apps (Flutter)
- Custom Flutter applications for each business
- Web, desktop, and mobile deployment
- Built using vertoie_ui component library
- Generated from LLM analysis of business requirements
- Version-controlled with testing and rollback capabilities

### 3. AI Generation Engine
- LLM integration for business analysis (Groq/Meta-LLaMA)
- Flutter code generation from specifications
- Dynamic data model creation using JSONB
- Module template system
- Schema evolution and migration generation

### 4. Data Architecture
- Multi-schema PostgreSQL design
- JSONB for flexible data storage
- Separate schemas: `vertoie` (platform) + `user_{uuid}` (business data)
- Version management for schema evolution
- Forward/backward compatibility support

## Current Development Status
- **Phase 1**: Foundation (✅ Complete) - Project structure, database, auth, basic API
- **Phase 2**: AI Integration (🔄 Current) - LLM integration, chat interface, module recommendations
- **Phase 3**: Generation & Testing (📋 Planned) - Flutter scaffold generation, version management
- **Phase 4**: Production Readiness (📋 Planned) - Deployment, optimization, billing

## Key Technologies
- **Backend**: Go 1.24, Fiber framework, PostgreSQL 16
- **Frontend**: Flutter 3.32, Dart 3.8.1, vertoie_ui components
- **Web**: Go/Fiber + HTMX, Tailwind CSS
- **AI**: Groq API with Meta LLaMA models
- **Development**: Nix flakes, direnv, VS Code workspace
- **Database**: PostgreSQL with JSONB, multi-schema design

## Project Structure
```
vertoie/
├── 🚀 platform/          # Core platform services
│   ├── backend/          # Go API server
│   ├── frontend/         # Flutter mobile/web app
│   └── reference/        # Platform documentation
├── 🌐 web/              # Go web server & static site
├── 🧩 components/       # Flutter UI component library
│   └── vertoie_ui/      # Reusable Flutter components
├── 🎨 brand/           # Brand assets & guidelines
├── migrations/         # Database migrations
├── models/            # Shared Go models
└── nix/              # Development environment
```

## Development Environment
- Uses Nix flakes for reproducible development
- All commands should use `.envrc` with `nix` commands
- Multi-root VS Code workspace configuration
- Hot reload enabled for all services
- Integrated database migration system