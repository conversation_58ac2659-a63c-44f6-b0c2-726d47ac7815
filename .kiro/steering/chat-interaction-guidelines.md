# Chat Interaction Guidelines

## Response Requirements
- Always ask for clarification if requirements are unclear
- Provide specific, actionable code examples when explaining concepts
- Reference the Vertoie architecture (LLM integration, multi-schema DB, Flutter UI)
- Explain WHY architectural decisions are made, not just WHAT to do

## Code Examples Standards
- Always show complete, working examples that follow Vertoie patterns
- Include proper error handling in all code examples
- Use Vertoie naming conventions (VButton, VTextField, etc.)
- Show both good and bad examples when explaining concepts

## LLM-Specific Guidance
- When discussing LLM integration, always mention validation and error handling
- Provide examples of prompt engineering for business analysis
- Explain how to handle dynamic JSONB schemas from LLM responses
- Show proper caching strategies for LLM responses

## Architecture Explanations
- Always explain how code fits into the overall Vertoie architecture
- Mention multi-schema database implications when relevant
- Explain Flutter component design for dynamic rendering
- Reference the vertoie_ui library patterns

## Task Management
- Remind about updating STATUS.md and TASKS.md when discussing task completion
- Break down complex tasks into smaller, manageable steps
- Provide clear acceptance criteria for each suggested solution
- Always mention testing requirements for new features

## Problem-Solving Approach
- Start by understanding the business context
- Consider how the solution works with LLM-generated modules
- Think about scalability across different business types
- Always consider security implications, especially with user-generated content

## Communication Style
- Be direct and specific - avoid vague suggestions
- Use Vertoie terminology consistently
- Provide concrete next steps
- Ask follow-up questions when context is missing

## Code Comments Policy

### Comment Guidelines
- **Minimal Comments**: Only comment when absolutely necessary for understanding
- **Function/Method Level Only**: Comments should be at function/method definitions, not inline
- **Documentation Purpose**: Comments are for API documentation, not code explanation
- **Self-Documenting Code**: Write code that explains itself through clear naming and structure

### When to Comment

```dart
// ✅ Good: Method documentation for public API
/// Creates a new calendar event with the specified parameters.
/// 
/// Returns null if the event conflicts with existing availability rules.
/// Throws [ValidationException] if required fields are missing.
VCalendarEvent? createEvent(String title, DateTime startTime);

// ✅ Good: Complex business logic explanation
/// Calculates availability windows considering business hours, 
/// buffer times, and existing appointments.
List<TimeSlot> calculateAvailability();
```

```go
// ✅ Good: Function documentation for public API
// AnalyzeBusiness processes a business description and returns
// AI-generated module recommendations and data models.
// Returns error if LLM analysis fails or business type is unsupported.
func (s *llmService) AnalyzeBusiness(ctx context.Context, description string) (*BusinessAnalysis, error) {
    // Implementation...
}
```

### What NOT to Comment

```dart
// ❌ Bad: Obvious inline comments
final theme = VTheme.of(context); // Get theme from context
if (isEnabled) { // Check if button is enabled
  // Handle button press
  onPressed?.call();
}

// ❌ Bad: Implementation details
// Using setState to update the UI
setState(() {
  _selectedDate = newDate;
});
```

```go
// ❌ Bad: Obvious inline comments
user := getUserFromDB(userID) // Get user from database
if user != nil { // Check if user exists
    // Process user
    processUser(user)
}
```

### Comment Standards
- Use `///` for Dart public API documentation (generates docs)
- Use `//` for Go function documentation and minimal internal notes
- Keep comments concise and focused on "why" not "what"
- Update comments when code changes or remove them entirely
- Never leave TODO comments in production code
- Avoid commenting out code - delete it instead (version control preserves history)