# Database Standards

## Critical Migration Requirements
- **ALL** migrations must be wrapped in BEGIN...COMMIT transactions
- Include rollback instructions in migration comments
- Test migrations on sample data before applying
- Use descriptive migration filenames with timestamps

```sql
-- ✅ Good: Proper migration structure
BEGIN;

-- Migration: Add business_analysis table
-- Rollback: DROP TABLE business_analysis;

CREATE TABLE IF NOT EXISTS business_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    business_description TEXT NOT NULL,
    analysis_result JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_business_analysis_user_id ON business_analysis(user_id);
CREATE INDEX idx_business_analysis_created_at ON business_analysis(created_at);

COMMIT;
```

## Multi-Schema Architecture
- Use `vertoie` schema for platform data (users, subscriptions, etc.)
- Create separate schemas for each user's business data
- Schema naming: `user_{uuid}` for user-specific data
- Use proper schema isolation and permissions

## JSONB for Dynamic Schemas
- Store LLM-generated data models as JSONB
- Use proper indexing for JSONB queries: `CREATE INDEX ON table USING GIN (jsonb_column);`
- Validate JSONB structure at application level
- Use JSONB operators efficiently: `->`, `->>`, `@>`, `?`

```go
// ✅ Good: JSONB query optimization
func (r *moduleRepository) FindModulesByBusinessType(ctx context.Context, businessType string) ([]Module, error) {
    query := `
        SELECT id, name, configuration 
        FROM modules 
        WHERE configuration @> $1
    `
    params := fmt.Sprintf(`{"business_type": "%s"}`, businessType)
    
    rows, err := r.db.QueryContext(ctx, query, params)
    if err != nil {
        return nil, fmt.Errorf("failed to query modules: %w", err)
    }
    defer rows.Close()
    
    return r.scanModules(rows)
}
```

## Primary Key Standards
- Use UUIDs for all primary keys: `id UUID PRIMARY KEY DEFAULT gen_random_uuid()`
- Use meaningful foreign key names: `user_id`, `business_id`, `module_id`
- Always include created_at and updated_at timestamps
- Use soft deletes with deleted_at for audit trails

## Performance Standards
- Create indexes for all foreign keys
- Index JSONB columns that are frequently queried
- Use partial indexes for soft deletes: `WHERE deleted_at IS NULL`
- Monitor query performance and optimize slow queries

## Audit and Security
- Implement audit logs for sensitive operations
- Use row-level security (RLS) where appropriate
- Encrypt sensitive fields at application level
- Regular database backups and point-in-time recovery

## Connection Management
- Use connection pooling (pgxpool for Go)
- Set appropriate connection limits
- Implement proper connection cleanup
- Handle connection failures gracefully