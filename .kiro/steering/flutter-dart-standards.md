# Flutter/Dart Standards

Dart version: 3.8.1
Flutter version: 3.32

## Null Safety
- Always use null safety
- Avoid `!` operator when possible - use `??` and `?.` operators instead
- Prefer nullable types over late variables when appropriate

## Widget Design
- Use const constructors whenever possible
- Prefer StatelessWidget over StatefulWidget when state isn't needed
- Keep widgets focused and under 20 lines
- Use composition over inheritance
- Extract complex widgets into separate files

## Dynamic Rendering for LLM Integration
- Design components to work with LLM-generated JSONB data models
- Use Map<String, dynamic> for flexible data structures
- Implement runtime validation for dynamic schemas

```dart
// ✅ Good: Component that adapts to LLM-generated data
class DynamicF<PERSON><PERSON>ield extends StatelessWidget {
  const DynamicFormField({
    super.key,
    required this.fieldDefinition, // LLM-generated field spec
    required this.onChanged,
  });

  final Map<String, dynamic> fieldDefinition;
  final ValueChanged<dynamic> onChanged;

  @override
  Widget build(BuildContext context) {
    final fieldType = fieldDefinition['type'] as String? ?? 'text';
    final isRequired = fieldDefinition['required'] as bool? ?? false;
    final label = fieldDefinition['label'] as String? ?? 'Field';
    
    return _buildFieldByType(fieldType, label, isRequired);
  }
}
```

## State Management
- Use Provider/Riverpod patterns consistently
- Keep state as local as possible
- Avoid global state when not necessary
- Use proper disposal for controllers and listeners

```dart
// ✅ Good: Proper state management
class VInput extends StatefulWidget {
  @override
  State<VInput> createState() => _VInputState();
}

class _VInputState extends State<VInput> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
```

## Vertoie UI Library Standards
- All components must support the adaptive theming system
- Use discrete theme options, not infinite customization
- Implement accessibility support (screen readers, keyboard navigation)
- Follow the VButton, VTextField naming convention

## Component Naming Conventions
- All components must start with `V` prefix: `VButton`, `VInput`, `VCalendar`
- Use descriptive names: `VDatePicker` not `VPicker`
- Group related components: `VCheckbox`, `VCheckboxGroup`
- Variants use descriptive suffixes: `VButtonVariant.primary`, `VInputType.email`

## Testing Requirements
- Write widget tests for all components
- Use golden tests for visual regression testing
- Mock LLM responses for consistent testing
- Test with dynamic data structures

```dart
// ✅ Good: Comprehensive testing
testWidgets('VButton supports all variants', (tester) async {
  for (final variant in VButtonVariant.values) {
    await tester.pumpWidget(
      VTestApp(
        child: VButton(
          onPressed: () {},
          variant: variant,
          child: Text('Test'),
        ),
      ),
    );
    
    expect(find.byType(VButton), findsOneWidget);
    await expectLater(
      find.byType(VButton),
      matchesGoldenFile('button_${variant.name}.png'),
    );
  }
});
```

## Accessibility Requirements
- All interactive components must have proper semantics
- Use `Semantics` widget for custom components
- Provide meaningful labels and hints
- Support voice-over navigation

```dart
// ✅ Good: Accessibility support
return Semantics(
  label: 'Calendar event: ${event.title}',
  hint: 'Double tap to edit event',
  child: GestureDetector(
    onTap: onEventTapped,
    child: eventWidget,
  ),
);
```