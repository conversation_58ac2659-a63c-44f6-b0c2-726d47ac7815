# Critical Rules - NEVER VIOLATE

## Environment & Development
- **ALWAYS** Use `.envrc` with `nix` commands. Use `direnv allow` or just `source .envrc`
- **DO NOT** do anything that was not explicitly asked of you
- **ALWAYS** update STATUS.md AND TASKS.md after each task completion
- **NEVER** ignore errors - always handle them explicitly

## Database Operations
- **ALL** database migrations must be wrapped in transaction BEGIN...COMMIT
- **ALL** related table inserts should be wrapped in a transaction BEGIN...COMMIT (ex: register user and magic link auth_token)
- **NEVER** create migrations without proper rollback instructions in comments
- **NEVER** use SELECT * in production code
- **NEVER** ignore database connection errors

## Security Standards
- **NEVER** trust user input without validation
- **NEVER** store passwords in plain text
- **NEVER** execute LLM-generated code without validation
- **NEVER** expose sensitive data in logs or error messages
- **NEVER** use string concatenation for SQL queries - always use parameterized queries

## LLM Integration
- **NEVER** assume LLM responses are valid without validation
- **NEVER** expose raw LLM prompts containing sensitive data
- **NEVER** execute LLM-generated database queries directly
- **NEVER** trust LLM-generated schemas without validation

## Code Quality
- **NEVER** use magic numbers or hard-coded strings - use constants
- **NEVER** create God classes or functions that do too much
- **NEVER** use global variables or singletons
- **NEVER** ignore null safety in Dart (avoid `!` operator)
- **NEVER** swallow exceptions without proper handling

## Error Handling Requirements
- Go: Always check and return errors explicitly `if err != nil { return fmt.Errorf("context: %w", err) }`
- Dart: Use proper exception handling with try-catch blocks
- Never swallow errors silently
- Provide meaningful error messages with context

## Performance Requirements
- UI components must render in <16ms for 60fps
- API endpoints should respond in <200ms
- LLM operations should complete in <5s
- Database queries should execute in <50ms for simple operations

## Testing Never Rules
- **NEVER** skip testing for critical business logic
- **NEVER** write tests that depend on external services without mocks
- **NEVER** commit code that breaks existing tests
- **NEVER** test implementation details instead of behavior