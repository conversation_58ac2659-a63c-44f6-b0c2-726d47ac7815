/* Onboarding Styles - Clean Design */

/* Body and Main Setup */
.onboarding-body {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0;
}

.onboarding-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

/* Wrapper */
.onboarding-wrapper {
  flex: 1;
  background: var(--bg-secondary);
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Initial Card */
.onboarding-initial {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  min-height: auto;
  padding: var(--space-8) var(--space-6);
  width: 100%;
  max-width: 120rem;
  margin: 0 auto;
}

/* Onboarding Container */
.onboarding-container {
  flex: 1;
  background: var(--bg-secondary);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--space-6);
  overflow-y: auto;
}

.onboarding-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 100rem;
  width: 100%;
  overflow: hidden;
  transition: all var(--transition-fast);
}

.onboarding-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.onboarding-header h2 {
  font-size: 2.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.onboarding-header p {
  font-size: 1.6rem;
  color: var(--text-secondary);
  margin: 0;
}

.onboarding-card form {
  padding: var(--space-6);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-input {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  font-size: 1.6rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  color: var(--text-primary);
  transition: all 0.2s ease;
  line-height: 1.5;
}

.form-input:focus {
  outline: none;
  border-color: var(--vertoie-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
  opacity: 0.8;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: 1.6rem;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  outline: none;
}

.btn:focus-visible {
  outline: 2px solid var(--vertoie-orange);
  outline-offset: 2px;
}

.btn-primary {
  background: #ff6b35;
  color: white;
  font-weight: 600;
  font-size: 1.8rem;
  padding: var(--space-4) var(--space-6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background: var(--vertoie-orange-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:focus-visible {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

.btn-block {
  width: 100%;
  padding: var(--space-4) var(--space-6);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: 1.8rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: 1.8rem;
}

.btn-icon {
  padding: var(--space-3);
  width: 4.4rem;
  height: 4.4rem;
}

.examples,
.card-footer {
  padding: var(--space-8) var(--space-6);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.card-header {
  padding: var(--space-8) var(--space-6);
  border-bottom: 2px solid var(--border-light);
  background: var(--bg-primary);
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-8);
  align-items: center;
}

.card-header-content h2 {
  font-size: 3.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.card-header-description {
  font-size: 1.6rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

.card-body {
  padding: var(--space-8) var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  background: var(--bg-primary);
  border-bottom: 2px solid var(--border-light);
}

.card-body .btn-primary {
  align-self: center;
  width: auto;
  min-width: 24rem;
  max-width: 32rem;
}

.examples-title {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.examples ul,
.examples-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.examples li,
.examples-list li {
  font-size: 1.4rem;
  color: var(--text-secondary);
  padding: var(--space-2) 0;
  padding-left: var(--space-8);
  position: relative;
  line-height: 1.2;
}

.examples li::before,
.examples-list li::before {
  content: "→";
  position: absolute;
  left: 0;
  top: var(--space-2);
  color: var(--vertoie-orange);
  font-size: 1.4rem;
  font-weight: 600;
}

/* Split Layout */
.onboarding-split {
  display: grid;
  grid-template-columns: 33% 1fr;
  gap: 0;
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

/* Chat Panel */
.chat-panel {
  background: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.chat-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-light);
  flex-shrink: 0;
}

.chat-header h3 {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chat-header p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin: var(--space-1) 0 0;
}

.chat-messages {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  min-height: 0;
}

.message {
  max-width: 85%;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  align-self: flex-end;
}

.message-assistant {
  align-self: flex-start;
}

.message-content {
  padding: var(--space-3) var(--space-4);
  border-radius: 12px;
  font-size: 1.4rem;
  line-height: 1.6;
}

.message-user .message-content {
  background: var(--vertoie-orange);
  color: white;
}

.message-assistant .message-content {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.chat-input-wrapper {
  padding: var(--space-3);
  border-top: 1px solid var(--border-light);
  flex-shrink: 0;
}

.chat-input-wrapper form {
  display: flex;
  gap: var(--space-3);
}

.chat-input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  font-size: 1.4rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.chat-input:focus {
  outline: none;
  border-color: var(--vertoie-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

/* Modules Panel */
.modules-panel {
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
  min-height: 0;
}

.modules-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-shrink: 0;
}

.modules-header h2 {
  font-size: 2.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.business-type {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin: var(--space-1) 0 0;
}

.pricing-summary {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: var(--space-3);
  min-width: 20rem;
}

.pricing-summary h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pricing-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--vertoie-orange);
  margin: var(--space-1) 0;
}

.pricing-details {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Modules Container */
.modules-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--space-4);
  min-height: 0;
}

.modules-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 30rem;
}

.modules-loading p {
  font-size: 1.6rem;
  color: var(--text-secondary);
  margin-top: var(--space-4);
}

/* Modules Grid */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(32rem, 1fr));
  gap: var(--space-3);
}

.module-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: var(--space-5);
  transition: all 0.2s ease;
  cursor: pointer;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.module-card.selected {
  border-color: var(--vertoie-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.module-name {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.module-tags {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.2rem 0.8rem;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: capitalize;
}

.tag-core {
  background: rgba(255, 107, 53, 0.1);
  color: var(--vertoie-orange);
}

.tag-efficiency {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.tag-growth {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.tag-score {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.module-checkbox {
  width: 2.4rem;
  height: 2.4rem;
  cursor: pointer;
  accent-color: var(--vertoie-orange);
}

.module-description {
  font-size: 1.4rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 var(--space-3) 0;
}

.module-benefit {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-light);
  font-size: 1.3rem;
  color: var(--text-tertiary);
  font-style: italic;
}

.icon-benefit {
  width: 1.6rem;
  height: 1.6rem;
  color: var(--vertoie-orange);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-8);
}

.empty-icon {
  width: 6rem;
  height: 6rem;
  color: var(--text-muted);
  margin: 0 auto var(--space-4);
}

.empty-state h3 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-state p {
  font-size: 1.6rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Complete Wrapper */
.complete-wrapper {
  padding-top: var(--space-6);
  text-align: center;
}

/* Utilities */
.icon {
  width: 2rem;
  height: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 4rem;
  height: 4rem;
  color: var(--vertoie-orange);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline-flex;
}

.htmx-request .btn-text {
  display: none;
}

/* Additional fixes for proper layout */
.onboarding-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Ensure modules-grid is the scrollable container */
#modules-grid {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
}

/* Responsive Design - Mobile First Approach */

/* Tablet Styles */
@media (max-width: 1024px) {
  .onboarding-split {
    grid-template-columns: 40% 1fr;
  }

  .onboarding-card {
    max-width: 44rem;
  }

  .onboarding-initial {
    padding: var(--space-6) var(--space-3);
  }

  .card-header {
    padding: var(--space-6) var(--space-5);
  }

  .card-body {
    padding: var(--space-6) var(--space-5);
  }

  .card-footer {
    padding: var(--space-6) var(--space-5);
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .onboarding-split {
    grid-template-columns: 1fr;
    grid-template-rows: 40vh 1fr;
    height: auto;
  }

  .chat-panel {
    border-right: none;
    border-bottom: 1px solid var(--border-light);
  }

  .modules-grid {
    grid-template-columns: 1fr;
  }

  .pricing-summary {
    width: 100%;
    margin-bottom: var(--space-4);
  }

  .modules-header {
    flex-direction: column;
    gap: var(--space-4);
  }

  /* Onboarding Card Mobile Adjustments */
  .onboarding-card {
    max-width: 100%;
    margin: var(--space-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
  }

  .onboarding-initial {
    padding: var(--space-4) var(--space-2);
    min-height: auto;
  }

  .onboarding-container {
    padding: var(--space-2);
    align-items: flex-start;
    padding-top: var(--space-6);
  }

  .card-header {
    padding: var(--space-6) var(--space-4);
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .card-header-content h2 {
    font-size: 2.4rem;
    line-height: 1.3;
  }

  .card-header-description {
    font-size: 1.5rem;
    line-height: 1.5;
  }

  .card-body {
    padding: var(--space-6) var(--space-4);
  }

  .card-footer {
    padding: var(--space-6) var(--space-4);
  }

  .form-input {
    padding: var(--space-4);
    font-size: 1.6rem;
  }

  .btn-primary {
    padding: var(--space-4) var(--space-5);
    font-size: 1.7rem;
    width: 100%;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .onboarding-card {
    margin: var(--space-2);
    border-radius: var(--radius-md);
  }

  .onboarding-container {
    padding: var(--space-1);
    padding-top: var(--space-4);
  }

  .card-header {
    padding: var(--space-5) var(--space-3);
  }

  .card-header h2 {
    font-size: 2.2rem;
  }

  .card-header p {
    font-size: 1.4rem;
  }

  .card-body {
    padding: var(--space-5) var(--space-3);
    gap: var(--space-5);
  }

  .card-footer {
    padding: var(--space-5) var(--space-3);
  }

  .examples-list li {
    font-size: 1.3rem;
    padding-left: var(--space-4);
  }

  .examples-list li::before {
    font-size: 1.4rem;
  }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
  .onboarding-card {
    max-width: 120rem;
  }

  .onboarding-initial {
    max-width: 140rem;
    padding: var(--space-10) var(--space-8);
  }

  .card-header {
    padding: var(--space-12) var(--space-10);
  }

  .card-body {
    padding: var(--space-10) var(--space-10);
  }

  .card-footer {
    padding: var(--space-10) var(--space-10);
  }
}
